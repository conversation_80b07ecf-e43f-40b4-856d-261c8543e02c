import pandas as pd


def read_template_files():
    """读取模板文件并显示其结构"""

    # 读取模板.xlsx
    print("=== 读取 模板.xlsx ===")
    try:
        # 使用pandas读取
        df_template = pd.read_excel("模板.xlsx", sheet_name=None)  # 读取所有sheet

        for sheet_name, df in df_template.items():
            print(f"\nSheet: {sheet_name}")
            print(f"Shape: {df.shape}")
            print("Content:")
            print(df.head(10))  # 显示前10行
            print("-" * 50)

    except Exception as e:
        print(f"读取模板.xlsx时出错: {e}")

    # 读取附件1文件
    print("\n=== 读取 附件1：XX档案归档文件目录（非会计凭证类）.xls ===")
    try:
        # 使用pandas读取
        df_attachment = pd.read_excel("附件1：XX档案归档文件目录（非会计凭证类）.xls", sheet_name=None)

        for sheet_name, df in df_attachment.items():
            print(f"\nSheet: {sheet_name}")
            print(f"Shape: {df.shape}")
            print("Content:")
            print(df.head(10))  # 显示前10行
            print("-" * 50)

    except Exception as e:
        print(f"读取附件1文件时出错: {e}")


if __name__ == "__main__":
    read_template_files()
